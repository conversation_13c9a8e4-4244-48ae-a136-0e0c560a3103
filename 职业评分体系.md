# 游戏职业评分体系 - 精简版（已优化）

## 核心数据特征

基于原评分体系的精简，本系统只需要以下14个核心数据特征即可完成所有评分计算：

### 个人基础战斗数据（7项）
- **对玩家伤害** - 人伤DPS评估核心，防守一团和防守二团评估核心
- **对建筑伤害** - 拆塔DPS评估核心，进攻一团和进攻二团评估核心
- **承受伤害** - 坦克和进攻二团评估核心
- **治疗值** - 素问评估核心
- **击败数** - 击杀效率评估
- **死亡数** - 负面惩罚指标
- **助攻数** - 团队协作指标

### 职业专属技能数据（3项）
- **化羽数** - 素问复活技能
- **焚骨数** - 九灵特殊技能
- **清泉数** - 潮光辅助技能

### 宏观战术数据（4项）
- **敌方总死亡数** - 焚骨转化率计算
- **资源获取数** - 资源型打法识别
- **所在分团** - 战术执行度评估
- **对局时长** - 数据缩放调整

## 职业评分公式

### 1. 素问（治疗师）- 无参考值评估方法

**动态基数评分 - 中位数版本（推荐）**
```
# 基于当局素问数据动态计算基数，使用中位数抗极值干扰
当局素问治疗中位数 = 当局所在团所有素问治疗值的中位数
当局素问承伤中位数 = 当局所在团所有玩家承受伤害的中位数
当局素问助攻中位数 = 当局所在团所有职业助攻数的中位数
当局素问死亡中位数 = 当局所在团所有职业死亡数的中位数

# 个人效率计算（相对于当局素问中位数水平）
治疗死亡比 = 治疗值/max(死亡数, 1)
治疗基准比 = max(当局素问治疗中位数/max(当局素问死亡中位数, 1), 1)
治疗效率 = min(治疗死亡比/治疗基准比, 3) * 33.33

化羽效率 = (化羽数/max(队伍总死亡数, 1)) * 100  # 复活成功率（绝对指标）

承伤死亡比 = 承受伤害/max(死亡数, 1)
承伤基准比 = max(当局素问承伤中位数/max(当局素问死亡中位数, 1), 1)
生存能力 = min(承伤死亡比/承伤基准比, 3) * 33.33

助攻死亡比 = 助攻数/max(死亡数, 1)
助攻基准比 = max(当局素问助攻中位数/max(当局素问死亡中位数, 1), 1)
团队贡献 = min(助攻死亡比/助攻基准比, 3) * 33.33


素问评分 = 治疗效率*0.35 + 化羽效率*0.25 + 生存能力*0.2 + 团队贡献*0.2
```

### 2. 铁衣/沧澜（双形态）- 无基准值评估方法

**形态判定 (平滑加权准则)：**
```
总输出 = 对玩家伤害 + 对建筑伤害
形态系数 = 承受伤害/max(总输出, 1)
if 形态系数 >= 4.5:
    坦克权重 = 1.0
elif 形态系数 <= 1.5:
    坦克权重 = 0.0
else:
    坦克权重 = (形态系数 - 1.5) / (4.5 - 1.5) # 在1.5和4.5之间进行线性插值

输出权重 = 1.0 - 坦克权重
```

**坦克型评分 - 动态基数版本：**
```
# 基于当局铁衣/沧澜坦克型数据动态计算基数
当局坦克承伤中位数 = 当局所在团所有职业承受伤害的中位数
当局坦克助攻中位数 = 当局所在团所有职业助攻数的中位数
当局坦克死亡中位数 = 当局所在团所有职业死亡数的中位数

# 个人效率计算（相对于当局坦克中位数水平）
承伤死亡比 = 承受伤害/max(死亡数, 1)
承伤基准比 = max(当局坦克承伤中位数/max(当局坦克死亡中位数, 1), 1)
承伤效率 = min(承伤死亡比/承伤基准比, 3) * 33.33

助攻死亡比 = 助攻数/max(死亡数, 1)
助攻基准比 = max(当局坦克助攻中位数/max(当局坦克死亡中位数, 1), 1)
控制贡献 = min(助攻死亡比/助攻基准比, 2) * 50

# 死亡惩罚（相对于当局坦克中位数死亡数）
死亡惩罚系数 = max(当局坦克死亡中位数, 1)
存活评分 = max(100 - (死亡数/死亡惩罚系数)*50, 20)

坦克评分 = 承伤效率*0.5 + 控制贡献*0.3 + 存活评分*0.2
```

**输出型评分 - 动态基数版本：**
参见人伤DPS和拆塔DPS评分部分

### 3. 九灵（DPS+焚骨）- 无基准值评估方法

**九灵人伤动态基数评分 - 中位数版本（推荐）**
```
# 基于当局九灵数据动态计算基数，使用中位数抗极值干扰
当局九灵人伤中位数 = 当局所在团所有职业对玩家伤害的中位数
当局九灵击败中位数 = 当局所在团所有职业击败数的中位数
当局九灵死亡中位数 = 当局所在团所有职业死亡数的中位数
当局九灵焚骨中位数 = 当局所在团所有九灵焚骨数的中位数
当局敌方总死亡数 = 当局敌方总死亡数

# 个人效率计算（相对于当局九灵中位数水平）
击杀死亡比 = 击败数/max(死亡数, 1)
击杀基准比 = max(当局九灵击败中位数/max(当局九灵死亡中位数, 1), 1)
击杀效率 = min(击杀死亡比/击杀基准比, 3) * 33.33

伤害死亡比 = 对玩家伤害/max(死亡数, 1)
伤害基准比 = max(当局九灵人伤中位数/max(当局九灵死亡中位数, 1), 1)
伤害效率 = min(伤害死亡比/伤害基准比, 3) * 33.33

# 死亡惩罚（相对于当局九灵中位数死亡数）
死亡惩罚系数 = max(当局九灵死亡中位数, 1)
生存能力 = max(100 - (死亡数/死亡惩罚系数)*50, 20)

# 焚骨转化效率（相对于当局九灵中位数焚骨转化率）
个人焚骨转化率 = 焚骨数/max(敌方总死亡数, 1)
当局焚骨转化率中位数 = 当局九灵焚骨中位数/max(当局敌方总死亡数, 1)
焚骨转化基准 = max(当局焚骨转化率中位数, 0.01)  # 避免除零
焚骨效率 = min(个人焚骨转化率/焚骨转化基准, 3) * 33.33

# 动态权重调整
我方建筑推进劣势：(1 - 我方总建筑伤害 / 敌方总建筑伤害) * 100%
我方资源劣势：(敌方总资源获取 - 我方总资源获取) / (敌方总资源获取 + 我方总资源获取) * 100%

if 敌方总KDA/我方总KDA > 2 AND (我方建筑推进劣势 > 30% OR 我方资源劣势 > 40%):  # 逆风局
    伤害权重 = 0.55, 焚骨权重 = 0.05
else:  # 正常局
    伤害权重 = 0.4, 焚骨权重 = 0.2

九灵人伤评分 = 击杀效率*0.3 + 伤害效率*伤害权重 + 焚骨效率*焚骨权重 + 生存能力*0.1
```

**九灵拆塔动态基数评分 - 中位数版本（推荐）**
参见拆塔DPS评分部分

### 4. 潮光（辅助DPS）- 无基准值评估方法

**动态基数评分 - 中位数版本（推荐）**
```
# 基于当局潮光数据动态计算基数，使用中位数抗极值干扰
当局潮光总伤害中位数 = 当局所在团所有职业总伤害的中位数
当局潮光清泉中位数 = 当局所在团所有潮光清泉数的中位数
当局潮光治疗中位数 = 当局所在团所有潮光治疗值的中位数
当局潮光击败中位数 = 当局所在团所有职业击败数的中位数
当局潮光死亡中位数 = 当局所在团所有职业死亡数的中位数

# 个人效率计算（相对于当局潮光中位数水平）
总伤害 = 对玩家伤害 + 对建筑伤害
伤害死亡比 = 总伤害/max(死亡数, 1)
伤害基准比 = max(当局潮光总伤害中位数/max(当局潮光死亡中位数, 1), 1)
伤害效率 = min(伤害死亡比/伤害基准比, 3) * 33.33

# 辅助价值计算（相对于当局潮光中位数水平）
个人辅助值 = 清泉数*8 + 治疗值/100000  # 保持原有权重比例
当局辅助基准值 = 当局潮光清泉中位数*8 + 当局潮光治疗中位数/100000
辅助死亡比 = 个人辅助值/max(死亡数, 1)
辅助基准比 = max(当局辅助基准值/max(当局潮光死亡中位数, 1), 0.1)  # 避免除零
辅助价值 = min(辅助死亡比/辅助基准比, 3) * 33.33

# 平衡性计算（相对于当局潮光中位数水平）
击败死亡比 = 击败数/max(死亡数, 1)
击败基准比 = max(当局潮光击败中位数/max(当局潮光死亡中位数, 1), 1)
平衡性 = min(击败死亡比/击败基准比, 2) * 50

# 死亡惩罚（相对于当局潮光中位数死亡数）
死亡惩罚系数 = max(当局潮光死亡中位数, 1)
死亡惩罚 = max(0, 15 - (死亡数/死亡惩罚系数)*15)

潮光评分 = (伤害效率*0.4 + 辅助价值*0.35 + 平衡性*0.25) + 死亡惩罚
```

### 5. 纯DPS职业（血河、神相、龙吟、玄机）(平滑加权判定方法)
**类型判定：**
```
人伤比例 = 对玩家伤害/max(对玩家伤害 + 对建筑伤害, 1)
if 人伤比例 >= 0.7:
    人伤权重 = 1.0
elif 人伤比例 <= 0.3:
    人伤权重 = 0.0
else:
    人伤权重 = (人伤比例 - 0.3) / (0.7 - 0.3)

拆塔权重 = 1.0 - 人伤权重
```

**人伤DPS评分 - 动态基数版本（无基准值依赖）：**
```
# 基于当局人伤DPS数据动态计算基数，使用中位数抗极值干扰
当局人伤DPS人伤中位数 = 当局所在团所有职业对玩家伤害的中位数
当局人伤DPS击败中位数 = 当局所在团所有职业击败数的中位数
当局人伤DPS助攻中位数 = 当局所在团所有职业助攻数的中位数
当局人伤DPS死亡中位数 = 当局所在团所有职业死亡数的中位数

# 个人效率计算（相对于当局人伤DPS中位数水平）
击败死亡比 = 击败数/max(死亡数, 1)
击败基准比 = max(当局人伤DPS击败中位数/max(当局人伤DPS死亡中位数, 1), 1)
击杀效率 = min(击败死亡比/击败基准比, 3) * 33.33

伤害死亡比 = 对玩家伤害/max(死亡数, 1)
伤害基准比 = max(当局人伤DPS人伤中位数/max(当局人伤DPS死亡中位数, 1), 1)
伤害效率 = min(伤害死亡比/伤害基准比, 3) * 33.33

助攻死亡比 = 助攻数/max(死亡数, 1)
助攻基准比 = max(当局人伤DPS助攻中位数/max(当局人伤DPS死亡中位数, 1), 1)
团队贡献 = min(助攻死亡比/助攻基准比, 3) * 33.33

人伤DPS评分 = 击杀效率*0.4 + 伤害效率*0.4 + 团队贡献*0.2
```

**拆塔DPS评分 - 动态基数版本（无基准值依赖）：**
```
# 基于当局拆塔DPS数据动态计算基数，使用中位数抗极值干扰
当局拆塔DPS建筑伤害中位数 = 当局所在团所有职业对建筑伤害的中位数
当局拆塔DPS死亡中位数 = 当局所在团所有职业死亡数的中位数

# 个人效率计算（相对于当局拆塔DPS中位数水平）
建筑伤害死亡比 = 对建筑伤害/max(死亡数, 1)
建筑伤害基准比 = max(当局拆塔DPS建筑伤害中位数/max(当局拆塔DPS死亡中位数, 1), 1)
建筑效率 = min(建筑伤害死亡比/建筑伤害基准比, 3) * 33.33

# 推进价值（相对于当局拆塔DPS中位数建筑伤害）
推进价值基准 = max(当局拆塔DPS建筑伤害中位数, 1)
推进价值 = min(对建筑伤害/推进价值基准, 3) * 33.33

# 生存能力（相对于当局拆塔DPS中位数死亡数）
死亡惩罚系数 = max(当局拆塔DPS死亡中位数, 1)
生存能力 = max(100 - (死亡数/死亡惩罚系数)*50, 20)

拆塔DPS评分 = 建筑效率*0.5 + 推进价值*0.3 + 生存能力*0.2
```

## 战术执行度评估

### 进攻一团（拆塔团）- 动态基数版本（无基准值依赖）
```
# 基于当局进攻一团数据动态计算基数，使用中位数抗极值干扰
当局进攻一团建筑伤害中位数 = 当局所有进攻一团成员对建筑伤害的中位数
当局进攻一团死亡中位数 = 当局所有进攻一团成员死亡数的中位数

# 拆塔专注度（绝对指标，不需要基准值）
拆塔专注度 = 对建筑伤害/max(对玩家伤害 + 对建筑伤害, 1)

# 拆塔效率（相对于当局进攻一团中位数水平）
建筑伤害死亡比 = 对建筑伤害/max(死亡数, 1)
建筑伤害基准比 = max(当局进攻一团建筑伤害中位数/max(当局进攻一团死亡中位数, 1), 1)
拆塔效率 = min(建筑伤害死亡比/建筑伤害基准比, 3) * 33.33

# 生存执行（相对于当局进攻一团中位数死亡数）
死亡惩罚系数 = max(当局进攻一团死亡中位数, 1)
生存执行 = max(100 - (死亡数/死亡惩罚系数)*50, 30)

进攻一团执行度 = 拆塔专注度*0.4 + 拆塔效率*0.4 + 生存执行*0.2
```

### 进攻二团（诱饵团）- 动态基数版本（无基准值依赖）
```
# 基于当局进攻二团数据动态计算基数，使用中位数抗极值干扰
当局进攻二团承伤中位数 = 当局所有进攻二团成员承受伤害的中位数
当局进攻二团建筑伤害中位数 = 当局所有进攻二团成员对建筑伤害的中位数
当局进攻二团死亡中位数 = 当局所有进攻二团成员死亡数的中位数

# 诱饵效果（相对于当局进攻二团中位数承伤水平）
诱饵基准值 = max(当局进攻二团承伤中位数, 1)
诱饵效果 = min(承受伤害/诱饵基准值, 3) * 33.33

# 拖延价值（相对于当局进攻二团中位数水平）
个人拖延值 = (承受伤害 + 对建筑伤害)/max(死亡数, 1)
当局拖延基准值 = max((当局进攻二团承伤中位数 + 当局进攻二团建筑伤害中位数)/max(当局进攻二团死亡中位数, 1), 1)
拖延价值 = min(个人拖延值/当局拖延基准值, 3) * 33.33

# 拆塔贡献（相对于当局进攻二团中位数建筑伤害）
拆塔基准值 = max(当局进攻二团建筑伤害中位数, 1)
拆塔贡献 = min(对建筑伤害/拆塔基准值, 3) * 33.33

# 综合执行效率（相对于当局进攻二团中位数综合表现）
个人综合值 = 承受伤害 + 对建筑伤害
当局综合基准值 = max(当局进攻二团承伤中位数 + 当局进攻二团建筑伤害中位数, 1)
综合执行效率 = min(个人综合值/当局综合基准值, 3) * 33.33

# 生存执行（相对于当局进攻二团中位数死亡数）
死亡惩罚系数 = max(当局进攻二团死亡中位数, 1)
生存执行 = max(100 - (死亡数/死亡惩罚系数)*50, 30)

进攻二团执行度 = 诱饵效果*0.25 + 拆塔贡献*0.25 + 拖延价值*0.25 + 综合执行效率*0.15 + 生存执行*0.1
```

### 防守团（人伤团，有时可能分成2个团）- 动态基数版本（无基准值依赖）
*** 如果分为2个团，则需要分2个团计算 ***
```
# 基于当局防守团数据动态计算基数，使用中位数抗极值干扰
当局防守团人伤中位数 = 当局所有防守团成员对玩家伤害的中位数
当局防守团击败中位数 = 当局所有防守团成员击败数的中位数
当局防守团死亡中位数 = 当局所有防守团成员死亡数的中位数

# 人伤专注度（绝对指标，不需要基准值）
人伤专注度 = 对玩家伤害/max(对玩家伤害 + 对建筑伤害, 1)

# 击杀效率（相对于当局防守团中位数水平）
击败死亡比 = 击败数/max(死亡数, 1)
击败基准比 = max(当局防守团击败中位数/max(当局防守团死亡中位数, 1), 1)
击杀效率 = min(击败死亡比/击败基准比, 3) * 33.33

# 人伤输出（相对于当局防守团中位数水平）
人伤死亡比 = 对玩家伤害/max(死亡数, 1)
人伤基准比 = max(当局防守团人伤中位数/max(当局防守团死亡中位数, 1), 1)
人伤输出 = min(人伤死亡比/人伤基准比, 3) * 33.33

# 防守稳定性（相对于当局防守团中位数死亡数）
死亡惩罚系数 = max(当局防守团死亡中位数, 1)
防守稳定性 = max(100 - (死亡数/死亡惩罚系数)*50, 40)

防守团执行度 = 人伤专注度*0.35 + 击杀效率*0.3 + 人伤输出*0.25 + 防守稳定性*0.1
```

## 多团配合度评估

*** 如果防守团分为2个团，则需要分2个团计算。也就是计算四团配合度 ***

基于现有数据计算团队协同效果，无需额外数据收集：

### 1. 分工明确度评估
```
# 各团专业化程度
进攻一团专业化 = Σ(进攻一团成员建筑伤害专注度) / 进攻一团人数
防守团专业化 = Σ(防守团成员人伤专注度) / 防守团人数
进攻二团诱饵度 = Σ(进攻二团成员诱饵效果) / 进攻二团人数

分工明确度 = (进攻一团专业化 + 防守团专业化 + 进攻二团诱饵度) / 3
```

### 2. 协同效果评估 - 动态基数版本（无基准值依赖）
```
# 基于当局数据动态计算基数，使用中位数抗极值干扰
当局进攻团建筑伤害中位数 = 当局所有进攻团人员（一团+二团）建筑伤害的中位数
当局防守团击败中位数 = 当局所有防守团（一团+二团）击败数的中位数
当局防守团人伤中位数 = 当局所有防守团（一团+二团）人伤的中位数

# 多团总体战果（相对于当局中位数水平）
我方进攻建筑伤害总和 = 进攻一团建筑伤害总和 + 进攻二团建筑伤害总和
进攻基准值 = max(当局进攻团建筑伤害中位数, 1)
进攻总效果 = min(我方进攻建筑伤害总和/进攻基准值, 3) * 33.33

我方防守击败总和 = 防守团（一团+二团）击败数总和
我方防守人伤总和 = 防守团（一团+二团）人伤总和
防守击败基准值 = max(当局防守团击败中位数, 1)
防守人伤基准值 = max(当局防守团人伤中位数, 1)
防守总效果 = min((我方防守击败总和/防守击败基准值 + 我方防守人伤总和/防守人伤基准值)/2, 3) * 33.33

协同效果 = 进攻总效果*0.6 + 防守总效果*0.4
```

### 3. 资源分配合理性评估 - 动态基数版本（无基准值依赖）
```
# 基于当局数据动态计算基数，使用中位数抗极值干扰
当局资源获取中位数 = 当局所有玩家资源获取数的中位数
当局击败数中位数 = 当局所有玩家击败数的中位数
当局建筑伤害中位数 = 当局所有玩家对建筑伤害的中位数

# 资源效率评估（相对于当局中位数水平）
我方总资源获取 = Σ(所有成员资源获取数)
我方总击败数 = Σ(所有成员击败数)
我方总建筑伤害 = Σ(所有成员对建筑伤害)

资源获取基准值 = max(当局资源获取中位数 * 队伍人数, 1)
击败数基准值 = max(当局击败数中位数 * 队伍人数, 1)
建筑伤害基准值 = max(当局建筑伤害中位数 * 队伍人数, 1)

资源获取效率 = min(我方总资源获取/资源获取基准值, 2) * 50
击败转化效率 = min(我方总击败数/击败数基准值, 2) * 50
建筑推进效率 = min(我方总建筑伤害/建筑伤害基准值, 2) * 50

# 资源分配均衡度（绝对指标，不需要基准值）
资源分配方差 = 各成员资源获取数的方差
资源分配均值 = 各成员资源获取数的均值
资源分配均衡度 = max(100 - (资源分配方差/max(资源分配均值, 1))*50, 20)

资源分配合理性 = (资源获取效率*0.3 + 击败转化效率*0.3 + 建筑推进效率*0.2 + 资源分配均衡度*0.2)
```

### 4. 团队配合度综合评分
```
团队配合度 = (
    分工明确度 * 0.4 +      # 各团职责执行
    协同效果 * 0.35 +       # 整体战果
    资源分配合理性 * 0.25   # 资源配置
)
```

## 最终评分计算 - 无基准值版本

### 步骤1：个人职业表现计算
```
# 根据职业类型选择对应的评分公式
if 职业 == "素问":
    个人职业表现 = 素问评分
elif 职业 in ["铁衣", "沧澜"]:
    个人职业表现 = (坦克评分 * 坦克权重) + (输出评分 * 输出权重)
elif 职业 == "九灵":
    人伤比例 = 对玩家伤害/max(对玩家伤害 + 对建筑伤害, 1)
    个人职业表现 = (九灵人伤评分 * 人伤权重) + (拆塔DPS评分 * 拆塔权重)
elif 职业 == "潮光":
    个人职业表现 = 潮光评分
elif 职业 in ["血河", "神相", "龙吟", "玄机"]:
    人伤比例 = 对玩家伤害/max(对玩家伤害 + 对建筑伤害, 1)
    个人职业表现 = (人伤DPS评分 * 人伤权重) + (拆塔DPS评分 * 拆塔权重)
```

### 步骤2：个人战术执行度计算
```
# 根据所在分团选择对应的战术执行度评分公式
if 所在分团 == "进攻一团":
    个人战术执行度 = 进攻一团执行度
elif 所在分团 == "进攻二团":
    个人战术执行度 = 进攻二团执行度
elif 所在分团 == "防守团":
    个人战术执行度 = 防守团执行度
```

### 步骤3：基础评分计算（30分钟强制结算局适配）
```
# 由于30分钟强制结算，对局时长相对固定，简化时长调整逻辑
基础评分 = (
    个人职业表现 * 0.5 +
    个人战术执行度 * 0.35 +
    团队配合度 * 0.15
)

# 30分钟强制结算局的时长调整（简化版本）
if 对局时长 <= 15:      # 极短局（15分钟以下）
    时长调整系数 = 0.8
elif 对局时长 <= 25:    # 正常偏短局（15-25分钟）
    时长调整系数 = 0.95
elif 对局时长 <= 35:    # 正常局（25-30分钟，30分钟强制结算）
    时长调整系数 = 1.0

时长调整评分 = 基础评分 * 时长调整系数

# 步骤4：最终评分
最终评分 = min(时长调整评分, 100)
```

## 评分等级

### 个人评分等级
```
SS级：95-100分
S级：85-94分
A级：75-84分
B级：65-74分
C级：55-64分
D级：55分以下
```

### 团队配合度等级
```
S级：85-100分（优秀配合）
A级：70-84分（良好配合）
B级：60-69分（基本配合）
C级：50-59分（配合欠佳）
D级：50分以下（配合失败）
```
