HTML报告要求:

完整性: 报告中所有的数据（表格、名单等）、文字分析、指标计算公式以及Markdown报告的原始内容必须全部保留，不能有任何信息遗漏。所有图表应能够直观地展现关键数据（例如：队伍伤害对比、职业治疗排名、玩家击败数分布等）。

图表美观度与交互性:
图表应设计美观，配色协调，易于理解。请使用能在任意浏览器上直接正常显示的图表库实现，并确保图表具备基本的交互功能，例如鼠标悬停显示详细数据、缩放和数据筛选。

排版与美观:
整体布局应高级、现代感强，确保各个部分分隔清晰，如采用简洁的扁平化风格。
字体应清晰可辨，大小适中，易于阅读。
图片（如果生成图表）应高质量且清晰。
如果报告内容较长，请在报告顶部或侧边生成一个可点击的目录或导航栏。
Markdown中的代码块应进行语法高亮显示。

响应式设计:
确保在PC端和移动端都具备良好的可读性和用户体验。

本地运行:
生成的HTML文件必须是一个独立的、可脱机运行的单文件HTML报告，不依赖外部网络资源。

格式兼容性:
在转换过程中，请优先确保内容完整性，即使原始Markdown格式稍有不规范，也应尽可能正确渲染。